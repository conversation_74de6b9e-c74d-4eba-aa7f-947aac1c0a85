#!/usr/bin/env node

/**
 * 现代化 UI 测试脚本
 * 用于测试新的现代化界面功能
 */

const { spawn } = require('child_process');
const chalk = require('chalk');

console.log(chalk.blue.bold('🧪 现代化 UI 测试'));
console.log(chalk.gray('正在启动现代化界面...'));
console.log();

// 启动现代化界面
const child = spawn('node', ['dist/cli.js', '--modern'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

child.on('error', (error) => {
  console.error(chalk.red('❌ 启动失败:'), error.message);
  process.exit(1);
});

child.on('exit', (code) => {
  console.log(chalk.gray(`\n🏁 程序退出，代码: ${code}`));
  process.exit(code);
});

// 处理退出信号
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n⚠️  收到退出信号，正在关闭...'));
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log(chalk.yellow('\n⚠️  收到终止信号，正在关闭...'));
  child.kill('SIGTERM');
});
