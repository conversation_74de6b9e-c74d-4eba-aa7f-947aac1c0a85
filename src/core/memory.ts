import * as fs from 'fs';
import * as path from 'path';
import { ProjectScan } from '../utils/fileScanner';

export interface MemoryEntry {
  timestamp: Date;
  type: 'scan' | 'conversation' | 'insight' | 'todo';
  content: string;
  metadata?: Record<string, any>;
}

export interface ProjectMemory {
  projectPath: string;
  createdAt: Date;
  lastUpdated: Date;
  entries: MemoryEntry[];
  summary?: string;
}

export class MemoryManager {
  private memoryFile: string;
  private memory: ProjectMemory | null = null;

  constructor(projectPath: string = process.cwd()) {
    this.memoryFile = path.join(projectPath, '.chat.md');
  }

  public async loadMemory(): Promise<ProjectMemory> {
    if (this.memory) {
      return this.memory;
    }

    try {
      if (fs.existsSync(this.memoryFile)) {
        const content = await fs.promises.readFile(this.memoryFile, 'utf8');
        this.memory = this.parseMemoryFile(content);
      } else {
        this.memory = {
          projectPath: process.cwd(),
          createdAt: new Date(),
          lastUpdated: new Date(),
          entries: []
        };
      }
      return this.memory;
    } catch (error) {
      console.warn('加载记忆文件失败:', (error as Error).message);
      this.memory = {
        projectPath: process.cwd(),
        createdAt: new Date(),
        lastUpdated: new Date(),
        entries: []
      };
      return this.memory;
    }
  }

  public async addEntry(type: MemoryEntry['type'], content: string, metadata?: Record<string, any>): Promise<void> {
    const memory = await this.loadMemory();
    
    const entry: MemoryEntry = {
      timestamp: new Date(),
      type,
      content,
      metadata
    };

    memory.entries.push(entry);
    memory.lastUpdated = new Date();

    await this.saveMemory();
  }

  public async addScanResult(scan: ProjectScan, summary: string): Promise<void> {
    await this.addEntry('scan', summary, {
      totalFiles: scan.totalFiles,
      totalSize: scan.totalSize,
      fileTypes: scan.fileTypes,
      scannedAt: scan.scannedAt
    });
  }

  public async addConversationSummary(summary: string): Promise<void> {
    await this.addEntry('conversation', summary);
  }

  public async addInsight(insight: string): Promise<void> {
    await this.addEntry('insight', insight);
  }

  public async addTodo(todo: string): Promise<void> {
    await this.addEntry('todo', todo);
  }

  public async getRecentEntries(limit: number = 10): Promise<MemoryEntry[]> {
    const memory = await this.loadMemory();
    return memory.entries
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  public async getEntriesByType(type: MemoryEntry['type']): Promise<MemoryEntry[]> {
    const memory = await this.loadMemory();
    return memory.entries.filter(entry => entry.type === type);
  }

  public async updateSummary(summary: string): Promise<void> {
    const memory = await this.loadMemory();
    memory.summary = summary;
    memory.lastUpdated = new Date();
    await this.saveMemory();
  }

  private async saveMemory(): Promise<void> {
    if (!this.memory) return;

    const content = this.generateMemoryFile(this.memory);
    try {
      await fs.promises.writeFile(this.memoryFile, content, 'utf8');
    } catch (error) {
      console.error('保存记忆文件失败:', (error as Error).message);
    }
  }

  private generateMemoryFile(memory: ProjectMemory): string {
    let content = `# Chater 项目记忆

> 🧠 这是 Chater AI 的项目记忆文件，记录了项目的结构分析和重要对话内容
> 
> **项目路径**: ${memory.projectPath}  
> **创建时间**: ${memory.createdAt.toLocaleString('zh-CN')}  
> **最后更新**: ${memory.lastUpdated.toLocaleString('zh-CN')}

`;

    if (memory.summary) {
      content += `## 项目概述

${memory.summary}

`;
    }

    // 按类型分组显示条目
    const groupedEntries = memory.entries.reduce((groups, entry) => {
      if (!groups[entry.type]) {
        groups[entry.type] = [];
      }
      groups[entry.type].push(entry);
      return groups;
    }, {} as Record<string, MemoryEntry[]>);

    const typeNames = {
      scan: '📁 项目扫描',
      conversation: '💬 对话记录',
      insight: '💡 重要洞察',
      todo: '📝 待办事项'
    };

    for (const [type, entries] of Object.entries(groupedEntries)) {
      if (entries.length === 0) continue;

      content += `## ${typeNames[type as keyof typeof typeNames] || type}\n\n`;

      // 按时间倒序排列
      const sortedEntries = entries.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      for (const entry of sortedEntries) {
        content += `### ${entry.timestamp.toLocaleString('zh-CN')}\n\n`;
        content += `${entry.content}\n\n`;
        
        if (entry.metadata && Object.keys(entry.metadata).length > 0) {
          content += `<details>\n<summary>详细信息</summary>\n\n`;
          content += '```json\n';
          content += JSON.stringify(entry.metadata, null, 2);
          content += '\n```\n\n';
          content += '</details>\n\n';
        }
      }
    }

    content += `---
*此文件由 Chater AI 自动生成和维护*
`;

    return content;
  }

  private parseMemoryFile(content: string): ProjectMemory {
    // 这里实现一个简单的解析器
    // 在实际应用中可能需要更复杂的解析逻辑
    const lines = content.split('\n');
    
    let projectPath = process.cwd();
    let createdAt = new Date();
    let lastUpdated = new Date();
    
    // 尝试从文件中提取基本信息
    for (const line of lines) {
      if (line.includes('**项目路径**:')) {
        projectPath = line.split(':')[1]?.trim() || projectPath;
      }
      if (line.includes('**创建时间**:')) {
        const dateStr = line.split(':')[1]?.trim();
        if (dateStr) {
          createdAt = new Date(dateStr);
        }
      }
      if (line.includes('**最后更新**:')) {
        const dateStr = line.split(':')[1]?.trim();
        if (dateStr) {
          lastUpdated = new Date(dateStr);
        }
      }
    }

    return {
      projectPath,
      createdAt,
      lastUpdated,
      entries: [] // 简化实现，实际使用中需要解析所有条目
    };
  }

  public getMemoryFile(): string {
    return this.memoryFile;
  }

  public async hasMemory(): Promise<boolean> {
    return fs.existsSync(this.memoryFile);
  }
}