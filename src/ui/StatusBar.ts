import chalk from 'chalk';
import { existsSync, statSync, readFileSync } from 'fs';
import { join } from 'path';
import * as os from 'os';
import { execSync } from 'child_process';

export interface StatusInfo {
  project: {
    name: string;
    path: string;
    type: string;
    size: string;
  };
  memory: {
    initialized: boolean;
    size: string;
    lastModified: string;
  };
  model: {
    name: string;
    status: 'online' | 'offline' | 'loading';
    provider: string;
  };
  system: {
    cpu: string;
    memory: string;
    uptime: string;
  };
  shortcuts: {
    primary: string[];
    secondary: string[];
  };
}

/**
 * 状态栏和信息面板组件
 * 显示项目信息、模型状态、快捷键提示等
 */
export class StatusBar {
  private statusInfo: StatusInfo;
  private updateInterval: NodeJS.Timeout | null = null;
  private projectPath: string;

  constructor(projectPath: string = process.cwd()) {
    this.projectPath = projectPath;
    this.statusInfo = this.initializeStatus();
    this.startAutoUpdate();
  }

  /**
   * 渲染完整状态栏
   */
  public render(): void {
    this.updateStatus();
    
    console.log(this.renderHeader());
    console.log(this.renderProjectInfo());
    console.log(this.renderModelStatus());
    console.log(this.renderSystemInfo());
    console.log(this.renderShortcuts());
    console.log(this.renderFooter());
  }

  /**
   * 渲染紧凑状态栏
   */
  public renderCompact(): void {
    this.updateStatus();
    
    const projectIcon = this.getProjectIcon();
    const memoryIcon = this.statusInfo.memory.initialized ? '🧠' : '💭';
    const modelIcon = this.getModelStatusIcon();
    
    const statusLine = [
      `${projectIcon} ${chalk.cyan(this.statusInfo.project.name)}`,
      `${memoryIcon} ${this.statusInfo.memory.initialized ? chalk.green('记忆') : chalk.yellow('无记忆')}`,
      `${modelIcon} ${this.getModelStatusText()}`,
      `⚡ ${chalk.blue(this.statusInfo.shortcuts.primary.join(' | '))}`
    ].join(' │ ');
    
    const width = 80;
    const padding = Math.max(0, width - this.stripAnsi(statusLine).length - 4);
    
    console.log(chalk.gray('┌' + '─'.repeat(width - 2) + '┐'));
    console.log(chalk.gray('│ ') + statusLine + ' '.repeat(padding) + chalk.gray(' │'));
    console.log(chalk.gray('└' + '─'.repeat(width - 2) + '┘'));
  }

  /**
   * 渲染头部
   */
  private renderHeader(): string {
    const title = '🤖 Chater AI 开发助手';
    const version = 'v0.3.3';
    const width = 80;
    
    const titleLine = `${title} ${chalk.gray(version)}`;
    const padding = Math.max(0, width - this.stripAnsi(titleLine).length - 4);
    
    return [
      chalk.gray('┌' + '─'.repeat(width - 2) + '┐'),
      chalk.gray('│ ') + chalk.bold.blue(title) + ' ' + chalk.gray(version) + ' '.repeat(padding) + chalk.gray(' │'),
      chalk.gray('├' + '─'.repeat(width - 2) + '┤')
    ].join('\n');
  }

  /**
   * 渲染项目信息
   */
  private renderProjectInfo(): string {
    const { project, memory } = this.statusInfo;
    const width = 78;
    
    const projectLine = `📂 项目: ${chalk.cyan(project.name)} ${chalk.gray(`(${project.type})`)}`;
    const memoryLine = `🧠 记忆: ${memory.initialized ? 
      chalk.green('已初始化') + chalk.gray(` (${memory.size})`) : 
      chalk.yellow('未初始化')}`;
    
    const projectPadding = Math.max(0, width - this.stripAnsi(projectLine).length);
    const memoryPadding = Math.max(0, width - this.stripAnsi(memoryLine).length);
    
    return [
      chalk.gray('│ ') + projectLine + ' '.repeat(projectPadding) + chalk.gray(' │'),
      chalk.gray('│ ') + memoryLine + ' '.repeat(memoryPadding) + chalk.gray(' │')
    ].join('\n');
  }

  /**
   * 渲染模型状态
   */
  private renderModelStatus(): string {
    const { model } = this.statusInfo;
    const width = 78;
    
    const statusIcon = this.getModelStatusIcon();
    const statusText = this.getModelStatusText();
    
    const modelLine = `${statusIcon} 模型: ${chalk.cyan(model.name)} ${chalk.gray(`(${model.provider})`)} - ${statusText}`;
    const padding = Math.max(0, width - this.stripAnsi(modelLine).length);
    
    return [
      chalk.gray('├' + '─'.repeat(width) + '┤'),
      chalk.gray('│ ') + modelLine + ' '.repeat(padding) + chalk.gray(' │')
    ].join('\n');
  }

  /**
   * 渲染系统信息
   */
  private renderSystemInfo(): string {
    const { system } = this.statusInfo;
    const width = 78;
    
    const systemLine = `💻 系统: CPU ${system.cpu} │ 内存 ${system.memory} │ 运行时间 ${system.uptime}`;
    const padding = Math.max(0, width - this.stripAnsi(systemLine).length);
    
    return [
      chalk.gray('├' + '─'.repeat(width) + '┤'),
      chalk.gray('│ ') + systemLine + ' '.repeat(padding) + chalk.gray(' │')
    ].join('\n');
  }

  /**
   * 渲染快捷键
   */
  private renderShortcuts(): string {
    const { shortcuts } = this.statusInfo;
    const width = 78;
    
    const primaryLine = `⚡ 快捷键: ${shortcuts.primary.map(s => chalk.blue(s)).join(' │ ')}`;
    const secondaryLine = `💡 更多: ${shortcuts.secondary.map(s => chalk.gray(s)).join(' │ ')}`;
    
    const primaryPadding = Math.max(0, width - this.stripAnsi(primaryLine).length);
    const secondaryPadding = Math.max(0, width - this.stripAnsi(secondaryLine).length);
    
    return [
      chalk.gray('├' + '─'.repeat(width) + '┤'),
      chalk.gray('│ ') + primaryLine + ' '.repeat(primaryPadding) + chalk.gray(' │'),
      chalk.gray('│ ') + secondaryLine + ' '.repeat(secondaryPadding) + chalk.gray(' │')
    ].join('\n');
  }

  /**
   * 渲染底部
   */
  private renderFooter(): string {
    const width = 80;
    return chalk.gray('└' + '─'.repeat(width - 2) + '┘');
  }

  /**
   * 初始化状态信息
   */
  private initializeStatus(): StatusInfo {
    return {
      project: {
        name: '',
        path: '',
        type: '',
        size: ''
      },
      memory: {
        initialized: false,
        size: '',
        lastModified: ''
      },
      model: {
        name: 'deepseek-chat',
        status: 'online',
        provider: 'DeepSeek'
      },
      system: {
        cpu: '0%',
        memory: '0%',
        uptime: '0s'
      },
      shortcuts: {
        primary: ['/help', '!git', '@file', '#memory'],
        secondary: ['??建议', 'Tab补全', '↑↓历史', 'Ctrl+C退出']
      }
    };
  }

  /**
   * 更新状态信息
   */
  private updateStatus(): void {
    this.updateProjectInfo();
    this.updateMemoryInfo();
    this.updateSystemInfo();
  }

  /**
   * 更新项目信息
   */
  private updateProjectInfo(): void {
    const projectName = this.projectPath.split('/').pop() || 'unknown';
    const packageJsonPath = join(this.projectPath, 'package.json');
    
    let projectType = 'Unknown';
    if (existsSync(packageJsonPath)) {
      try {

        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));
        if (packageJson.dependencies?.react) projectType = 'React';
        else if (packageJson.dependencies?.vue) projectType = 'Vue';
        else if (packageJson.dependencies?.angular) projectType = 'Angular';
        else if (packageJson.dependencies?.express) projectType = 'Express';
        else if (packageJson.devDependencies?.typescript) projectType = 'TypeScript';
        else projectType = 'Node.js';
      } catch {
        projectType = 'Node.js';
      }
    }
    
    this.statusInfo.project = {
      name: projectName,
      path: this.projectPath,
      type: projectType,
      size: this.getProjectSize()
    };
  }

  /**
   * 更新记忆信息
   */
  private updateMemoryInfo(): void {
    const memoryFile = join(this.projectPath, 'CHATER.md');
    
    if (existsSync(memoryFile)) {
      const stat = statSync(memoryFile);
      this.statusInfo.memory = {
        initialized: true,
        size: this.formatFileSize(stat.size),
        lastModified: this.formatDate(stat.mtime)
      };
    } else {
      this.statusInfo.memory = {
        initialized: false,
        size: '',
        lastModified: ''
      };
    }
  }

  /**
   * 更新系统信息
   */
  private updateSystemInfo(): void {
    const _os = os;

    const _cpuUsage = process.cpuUsage();
    const memUsage = process.memoryUsage();
    const uptime = process.uptime();
    
    this.statusInfo.system = {
      cpu: '< 1%', // 简化显示
      memory: this.formatBytes(memUsage.heapUsed),
      uptime: this.formatUptime(uptime)
    };
  }

  /**
   * 获取项目图标
   */
  private getProjectIcon(): string {
    const { type } = this.statusInfo.project;
    const icons: Record<string, string> = {
      'React': '⚛️',
      'Vue': '💚',
      'Angular': '🅰️',
      'Express': '🚀',
      'TypeScript': '🔷',
      'Node.js': '💚'
    };
    return icons[type] || '📂';
  }

  /**
   * 获取模型状态图标
   */
  private getModelStatusIcon(): string {
    const { status } = this.statusInfo.model;
    const icons = {
      online: '🟢',
      offline: '🔴',
      loading: '🟡'
    };
    return icons[status] || '⚪';
  }

  /**
   * 获取模型状态文本
   */
  private getModelStatusText(): string {
    const { status } = this.statusInfo.model;
    const texts = {
      online: chalk.green('在线'),
      offline: chalk.red('离线'),
      loading: chalk.yellow('连接中')
    };
    return texts[status] || chalk.gray('未知');
  }

  /**
   * 获取项目大小
   */
  private getProjectSize(): string {
    try {
      const result = execSync(`du -sh "${this.projectPath}" 2>/dev/null || echo "0K"`, { encoding: 'utf8' });
      return result.split('\t')[0].trim();
    } catch {
      return '未知';
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + sizes[i];
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    return this.formatFileSize(bytes);
  }

  /**
   * 格式化日期
   */
  private formatDate(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`;
    return `${Math.floor(minutes / 1440)}天前`;
  }

  /**
   * 格式化运行时间
   */
  private formatUptime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) return `${hours}h${minutes}m`;
    if (minutes > 0) return `${minutes}m${secs}s`;
    return `${secs}s`;
  }

  /**
   * 移除ANSI颜色代码
   */
  private stripAnsi(str: string): string {
    return str.replace(/\x1b\[[0-9;]*m/g, '');
  }

  /**
   * 开始自动更新
   */
  private startAutoUpdate(): void {
    this.updateInterval = setInterval(() => {
      this.updateSystemInfo();
    }, 5000); // 每5秒更新一次系统信息
  }

  /**
   * 停止自动更新
   */
  public stopAutoUpdate(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  /**
   * 获取状态信息
   */
  public getStatus(): StatusInfo {
    this.updateStatus();
    return { ...this.statusInfo };
  }

  /**
   * 设置模型状态
   */
  public setModelStatus(status: 'online' | 'offline' | 'loading'): void {
    this.statusInfo.model.status = status;
  }

  /**
   * 更新快捷键
   */
  public updateShortcuts(primary: string[], secondary: string[]): void {
    this.statusInfo.shortcuts = { primary, secondary };
  }
}
