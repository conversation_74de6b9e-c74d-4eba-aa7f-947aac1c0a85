import chalk from 'chalk';
import inquirer from 'inquirer';
import { EventEmitter } from 'events';
import { InteractiveContext } from '../core/interactive';
import { EnhancedInput, InputSuggestion } from './EnhancedInput';
import { IntelliSense } from './IntelliSense';
import { StatusBar } from './StatusBar';
import { Animations } from './Animations';
import { ShortcutParser, ShortcutResult } from '../utils/shortcutParser';
import { exec } from 'child_process';
import { promisify } from 'util';
import ora from 'ora';

const execAsync = promisify(exec);

/**
 * 现代化 CLI 界面组件
 * 参考 Claude Code 的设计理念，提供更好的用户体验
 */
export class ModernCLI extends EventEmitter {
  private context: InteractiveContext;
  private running = false;
  private currentInput = '';
  private suggestions: string[] = [];
  private statusBar: StatusBar;
  private enhancedInput: EnhancedInput;
  private intelliSense: IntelliSense;
  private shortcutParser: ShortcutParser;
  private statusInfo = {
    project: '',
    model: '',
    memory: false,
    context: ''
  };

  constructor(context: InteractiveContext) {
    super();
    this.context = context;
    this.statusBar = new StatusBar(context.projectPath);
    this.enhancedInput = new EnhancedInput();
    this.intelliSense = new IntelliSense(context.projectPath);
    this.shortcutParser = new ShortcutParser(context.projectPath);
    this.updateStatusInfo();
  }

  /**
   * 启动现代化 CLI 界面
   */
  public async start(): Promise<void> {
    this.running = true;

    // 显示启动动画
    await Animations.showStartupAnimation();

    // 清屏并初始化界面
    this.clearScreen();
    this.statusBar.render();
    this.renderCommandHelp();

    // 主交互循环
    while (this.running) {
      try {
        await this.renderInputArea();
      } catch (error) {
        if (error instanceof Error && error.name === 'ExitPromptError') {
          await this.handleExit();
          break;
        }
        Animations.showError(error instanceof Error ? error.message : '未知错误');
      }
    }
  }

  /**
   * 渲染欢迎头部
   */
  private renderWelcomeHeader(): void {
    const version = '0.3.3';
    const title = chalk.bold.blue('🤖 Chater AI 开发助手');
    const versionText = chalk.gray(`v${version}`);
    
    console.log('┌' + '─'.repeat(60) + '┐');
    console.log(`│ ${title} ${versionText}${' '.repeat(60 - title.length - versionText.length - 2)}│`);
    console.log('├' + '─'.repeat(60) + '┤');
    console.log(`│ ${chalk.cyan('智能 AI 助手')}${' '.repeat(47)}│`);
    console.log('└' + '─'.repeat(60) + '┘');
    console.log();
  }

  /**
   * 渲染状态栏
   */
  private renderStatusBar(): void {
    const projectIcon = '📂';
    const modelIcon = '🤖';
    const memoryIcon = this.statusInfo.memory ? '🧠' : '💭';
    
    const statusItems = [
      `${projectIcon} ${chalk.cyan(this.statusInfo.project)}`,
      `${modelIcon} ${chalk.green(this.statusInfo.model)}`,
      `${memoryIcon} ${this.statusInfo.memory ? chalk.green('已初始化') : chalk.yellow('未初始化')}`
    ];
    
    const statusLine = statusItems.join(' │ ');
    console.log(chalk.gray('┌' + '─'.repeat(78) + '┐'));
    console.log(chalk.gray('│ ') + statusLine + chalk.gray(' '.repeat(78 - statusLine.length - 2) + '│'));
    console.log(chalk.gray('└' + '─'.repeat(78) + '┘'));
    console.log();
  }

  /**
   * 渲染命令帮助面板
   */
  private renderCommandHelp(): void {
    console.log(chalk.blue('📖 快速操作指南'));
    console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
    console.log('│ ' + chalk.blue('/help') + '         显示帮助    │ ' + chalk.green('!git status') + '   执行命令    │ ' + chalk.yellow('@file.ts') + '     查看文件 │');
    console.log('│ ' + chalk.blue('/config') + '       配置设置    │ ' + chalk.green('!npm build') + '    构建项目    │ ' + chalk.magenta('#insight') + '     记录洞察 │');
    console.log('│ ' + chalk.blue('/models') + '       模型状态    │ ' + chalk.green('!docker ps') + '    容器状态    │ ' + chalk.magenta('#todo') + '        待办事项 │');
    console.log('└─────────────────────────────────────────────────────────────────────────────┘');
    console.log();
  }

  /**
   * 渲染输入区域
   */
  private async renderInputArea(): Promise<void> {
    // 获取智能建议
    const suggestions = await this.generateInputSuggestions();

    // 使用增强输入组件
    const input = await this.enhancedInput.createInput(
      this.getFormattedPrompt(),
      {
        placeholder: '输入命令、问题或使用快捷键...',
        suggestions,
        syntaxHighlight: true,
        showPreview: true
      }
    );

    if (input.trim()) {
      await this.processInput(input);
    }
  }

  /**
   * 渲染输入提示
   */
  private renderInputPrompt(): void {
    const promptIcon = '>';
    const projectName = this.statusInfo.project;
    const prompt = chalk.cyan(`${promptIcon} ${projectName}`) + chalk.gray(' │ ');
    
    console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
    console.log('│ ' + chalk.gray('输入命令、问题或使用快捷键 (') + chalk.blue('?') + chalk.gray(' 显示帮助)') + ' '.repeat(35) + '│');
    console.log('├─────────────────────────────────────────────────────────────────────────────┤');
    console.log('│ ' + prompt + ' '.repeat(73 - prompt.length) + '│');
    console.log('└─────────────────────────────────────────────────────────────────────────────┘');
  }

  /**
   * 获取增强的用户输入
   */
  private async getEnhancedInput(): Promise<string> {
    const { input } = await inquirer.prompt({
      type: 'input',
      name: 'input',
      message: '',
      validate: (input: string) => {
        if (input.trim() === '?') {
          this.renderDetailedHelp();
          return true;
        }
        return true;
      },
      transformer: (input: string) => {
        // 实时显示建议
        if (input.length > 0) {
          this.updateSuggestions(input);
          return input + this.getSuggestionHint();
        }
        return input;
      }
    });

    return input;
  }

  /**
   * 更新建议列表
   */
  private updateSuggestions(input: string): void {
    // 这里可以集成现有的智能补全逻辑
    this.suggestions = this.generateSuggestions(input);
  }

  /**
   * 生成建议
   */
  private generateSuggestions(input: string): string[] {
    const suggestions: string[] = [];
    
    if (input.startsWith('/')) {
      suggestions.push('/help', '/config', '/models', '/memory', '/exit');
    } else if (input.startsWith('!')) {
      suggestions.push('!git status', '!npm build', '!docker ps', '!ls -la');
    } else if (input.startsWith('@')) {
      suggestions.push('@package.json', '@src/', '@README.md');
    } else if (input.startsWith('#')) {
      suggestions.push('#insight', '#todo', '#view');
    } else {
      suggestions.push('帮我分析项目结构', '生成代码文档', '优化性能');
    }
    
    return suggestions.filter(s => s.toLowerCase().includes(input.toLowerCase())).slice(0, 3);
  }

  /**
   * 获取建议提示
   */
  private getSuggestionHint(): string {
    if (this.suggestions.length > 0) {
      return chalk.gray(` (${this.suggestions.length} 个建议)`);
    }
    return '';
  }

  /**
   * 渲染详细帮助
   */
  private renderDetailedHelp(): void {
    console.log('\n' + chalk.blue('📚 详细帮助文档'));
    console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
    console.log('│ ' + chalk.yellow('快捷键类型') + ' '.repeat(66) + '│');
    console.log('├─────────────────────────────────────────────────────────────────────────────┤');
    console.log('│ ' + chalk.blue('/') + ' 命令快捷键  │ 执行内置命令 (help, config, models, memory, exit)     │');
    console.log('│ ' + chalk.green('!') + ' Bash快捷键  │ 执行系统命令 (git, npm, docker, ls 等)               │');
    console.log('│ ' + chalk.yellow('@') + ' 文件快捷键  │ 查看和操作文件 (支持 glob 模式匹配)                   │');
    console.log('│ ' + chalk.magenta('#') + ' 记忆快捷键  │ 管理项目记忆 (insight, todo, view, search)           │');
    console.log('├─────────────────────────────────────────────────────────────────────────────┤');
    console.log('│ ' + chalk.cyan('特殊操作') + ' '.repeat(66) + '│');
    console.log('│ ' + chalk.white('??') + '           │ 显示智能建议选择菜单                                 │');
    console.log('│ ' + chalk.white('history') + '      │ 查看命令历史记录                                     │');
    console.log('│ ' + chalk.white('clear') + '        │ 清屏并重新显示界面                                   │');
    console.log('└─────────────────────────────────────────────────────────────────────────────┘');
    console.log();
  }

  /**
   * 处理用户输入
   */
  private async processInput(input: string): Promise<void> {
    try {
      // 解析输入
      const result = this.shortcutParser.parseInput(input);

      // 处理不同类型的输入
      await this.handleShortcutResult(result);

      // 更新状态栏
      this.statusBar.renderCompact();
    } catch (error) {
      Animations.showError(error instanceof Error ? error.message : '处理失败');
    }
  }

  /**
   * 处理快捷键解析结果
   */
  private async handleShortcutResult(result: ShortcutResult): Promise<void> {
    switch (result.type) {
      case 'bash':
        await this.handleSystemCommand(result.content);
        break;
      case 'command':
        await this.handleInternalCommand(result.content);
        break;
      case 'filepath':
        await this.handleFilePath(result);
        break;
      case 'memory':
        await this.handleMemoryShortcut(result);
        break;
      case 'chat':
      default:
        await this.handleChat(result.content);
        break;
    }
  }

  /**
   * 处理系统命令
   */
  private async handleSystemCommand(command: string): Promise<void> {
    const spinner = ora(`执行: ${command}`).start();

    try {
      const { stdout, stderr } = await execAsync(command, {
        cwd: this.context.projectPath,
        maxBuffer: 1024 * 1024 // 1MB buffer
      });

      spinner.stop();

      if (stdout) {
        console.log(stdout);
      }

      if (stderr) {
        console.log(chalk.yellow(stderr));
      }
    } catch (error) {
      spinner.stop();
      const err = error as any;
      console.log(chalk.red(`❌ 命令执行失败: ${err.message}`));
      if (err.stdout) console.log(err.stdout);
      if (err.stderr) console.log(chalk.yellow(err.stderr));
    }
  }

  /**
   * 处理内置命令
   */
  private async handleInternalCommand(command: string): Promise<void> {
    const parts = command.split(' ');
    const commandName = parts[0].toLowerCase();
    const args = parts.slice(1);

    switch (commandName) {
      case 'help':
        this.showHelp();
        break;
      case 'exit':
        await this.handleExit();
        break;
      case 'config':
        console.log(chalk.yellow('配置功能开发中...'));
        break;
      case 'models':
        this.showModels();
        break;
      case 'memory':
        this.showMemory();
        break;
      case 'clear':
        this.clearScreen();
        this.statusBar.render();
        this.renderCommandHelp();
        break;
      default:
        console.log(chalk.red(`❌ 未知命令: ${commandName}`));
        console.log(chalk.gray('输入 "help" 查看可用命令'));
        break;
    }
  }

  /**
   * 显示帮助信息
   */
  private showHelp(): void {
    console.log(chalk.blue('\n📖 Chater AI 开发助手帮助\n'));

    console.log(chalk.yellow('🤖 AI 对话:'));
    console.log('  直接输入问题即可与 AI 对话');
    console.log('  例如: "帮我分析这个项目的架构"\n');

    console.log(chalk.yellow('🚀 快捷键:'));
    console.log('  /help         - 显示命令 (/ 为命令快捷键)');
    console.log('  /exit         - 优雅退出 chater');
    console.log('  !git status   - 执行 bash (! 为命令快捷键)');
    console.log('  @package.json - 查看文件 (@ 为文件快捷键)');
    console.log('  #重要发现     - 记录洞察 (# 为记忆快捷键)\n');

    console.log(chalk.yellow('🔧 内置命令:'));
    console.log('  help          - 显示帮助信息');
    console.log('  config        - 配置 API 密钥');
    console.log('  models        - 查看可用模型');
    console.log('  memory        - 查看项目记忆');
    console.log('  clear         - 清屏');
    console.log('  exit          - 退出程序\n');
  }

  /**
   * 显示模型信息
   */
  private showModels(): void {
    const availableModels = this.context.chat.getAvailableModels();

    console.log(chalk.blue('🤖 模型列表\n'));

    if (availableModels.length === 0) {
      console.log(chalk.red('❌ 无可用模型，请先配置 API 密钥'));
      return;
    }

    availableModels.forEach(model => {
      console.log(`  ${chalk.green('✓')} ${model}`);
    });
    console.log();
  }

  /**
   * 显示记忆信息
   */
  private showMemory(): void {
    if (!this.context.isProjectInitialized) {
      console.log(chalk.yellow('📝 项目记忆未初始化'));
      console.log(chalk.gray('💡 使用 "init" 命令初始化项目记忆\n'));
      return;
    }

    console.log(chalk.blue('🧠 项目记忆状态'));
    console.log(`📂 记忆文件: ${this.context.memory.getMemoryFile()}`);
    console.log();
  }

  /**
   * 处理文件路径
   */
  private async handleFilePath(result: ShortcutResult): Promise<void> {
    console.log(chalk.blue(`文件路径处理: ${result.content}`));
  }

  /**
   * 处理记忆快捷键
   */
  private async handleMemoryShortcut(result: ShortcutResult): Promise<void> {
    console.log(chalk.magenta(`记忆操作: ${result.content}`));
  }

  /**
   * 处理聊天
   */
  private async handleChat(content: string): Promise<void> {
    const availableModels = this.context.chat.getAvailableModels();
    if (availableModels.length === 0) {
      console.log(chalk.red('❌ 无可用模型，请先使用 "config" 命令配置 API 密钥'));
      return;
    }

    const spinner = ora({
      text: '🧠 Chater 正在思考... (Ctrl+C 强制退出)',
      color: 'cyan'
    }).start();

    try {
      const result = await this.context.chat.sendMessage(content);

      spinner.stop();

      // 显示使用的模型
      const modelDisplay = result.selectedModel === 'deepseek-reasoner'
        ? chalk.blue('🔬 deepseek-reasoner')
        : chalk.green('💬 deepseek-chat');
      console.log(chalk.gray(`[智能选择: ${modelDisplay}]`));

      console.log(chalk.magenta('\n🤖 AI: ') + result.response + '\n');
    } catch (error) {
      spinner.stop();

      console.log(chalk.red(`\n❌ 错误: ${error instanceof Error ? error.message : '未知错误'}`));
    }
  }

  /**
   * 渲染处理中状态
   */
  private renderProcessing(input: string): void {
    console.log(chalk.blue('⚡ 处理中...') + chalk.gray(` "${input}"`));
  }

  /**
   * 渲染成功消息
   */
  private renderSuccess(message: string): void {
    console.log(chalk.green('✅ ') + message);
    console.log();
  }

  /**
   * 渲染错误消息
   */
  private renderError(message: string): void {
    console.log(chalk.red('❌ 错误: ') + message);
    console.log();
  }

  /**
   * 更新状态信息
   */
  private updateStatusInfo(): void {
    this.statusInfo = {
      project: this.context.projectPath.split('/').pop() || 'chater',
      model: 'deepseek-chat',
      memory: this.context.isProjectInitialized,
      context: '5%'
    };
  }

  /**
   * 清屏
   */
  private clearScreen(): void {
    console.clear();
  }

  /**
   * 处理退出
   */
  private async handleExit(): Promise<void> {
    console.log(chalk.yellow('\n👋 感谢使用 Chater AI 开发助手！'));
    this.running = false;

    // 等待一小段时间让消息显示
    await new Promise(resolve => setTimeout(resolve, 500));

    // 强制退出
    process.exit(0);
  }

  /**
   * 生成输入建议
   */
  private async generateInputSuggestions(): Promise<InputSuggestion[]> {
    const completions = await this.intelliSense.getCompletions(this.currentInput);

    return completions.map(completion => ({
      text: completion.insertText,
      description: completion.detail,
      type: this.mapCompletionKindToSuggestionType(completion.kind),
      icon: completion.icon,
      priority: completion.priority
    }));
  }

  /**
   * 映射补全类型到建议类型
   */
  private mapCompletionKindToSuggestionType(kind: string): 'command' | 'file' | 'bash' | 'memory' | 'ai' | 'history' {
    const mapping = {
      'command': 'command' as const,
      'file': 'file' as const,
      'directory': 'file' as const,
      'bash': 'bash' as const,
      'git': 'bash' as const,
      'npm': 'bash' as const,
      'memory': 'memory' as const,
      'ai': 'ai' as const,
      'function': 'ai' as const,
      'variable': 'ai' as const
    };

    return (mapping as any)[kind] || 'ai';
  }

  /**
   * 获取格式化的提示符
   */
  private getFormattedPrompt(): string {
    const projectName = this.statusInfo.project || 'chater';
    const memoryIcon = this.statusInfo.memory ? '🧠' : '💭';
    return `${memoryIcon} ${projectName}`;
  }

  /**
   * 清理资源
   */
  public cleanup(): void {
    this.statusBar.stopAutoUpdate();
    Animations.stopAllAnimations();
  }

  /**
   * 停止 CLI
   */
  public stop(): void {
    this.running = false;
    this.cleanup();
  }
}
