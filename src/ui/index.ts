/**
 * 现代化 UI 组件导出
 * 统一导出所有 UI 相关组件
 */

export { ModernCLI } from './ModernCLI';
export { EnhancedInput, InputSuggestion, InputOptions } from './EnhancedInput';
export { IntelliSense, CompletionItem, CompletionKind } from './IntelliSense';
export { StatusBar, StatusInfo } from './StatusBar';
export { Animations, AnimationOptions, ThemeColors } from './Animations';

// 便捷的创建函数
import { ModernCLI } from './ModernCLI';
import { InteractiveContext } from '../core/interactive';

/**
 * 创建现代化 CLI 实例
 */
export function createModernCLI(context: InteractiveContext): ModernCLI {
  return new ModernCLI(context);
}

/**
 * 启动现代化 CLI 界面
 */
export async function startModernInterface(context: InteractiveContext): Promise<void> {
  const cli = createModernCLI(context);
  
  // 设置退出处理
  process.on('SIGINT', () => {
    cli.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    cli.stop();
    process.exit(0);
  });

  // 启动界面
  await cli.start();
}
