import chalk from 'chalk';
import { readdirSync, statSync, existsSync } from 'fs';
import { join, dirname, basename } from 'path';
import { glob } from 'glob';

export interface CompletionItem {
  label: string;
  detail: string;
  documentation: string;
  kind: CompletionKind;
  insertText: string;
  priority: number;
  icon: string;
  filterText?: string;
}

export enum CompletionKind {
  Command = 'command',
  File = 'file',
  Directory = 'directory',
  BashCommand = 'bash',
  GitCommand = 'git',
  NpmScript = 'npm',
  Memory = 'memory',
  AITemplate = 'ai',
  Variable = 'variable',
  Function = 'function'
}

/**
 * 智能补全系统
 * 提供类似 IDE 的智能补全功能
 */
export class IntelliSense {
  private projectPath: string;
  private commandHistory: string[] = [];
  private fileCache: Map<string, string[]> = new Map();
  private packageJson: any = null;

  constructor(projectPath: string = process.cwd()) {
    this.projectPath = projectPath;
    this.loadPackageJson();
    this.loadCommandHistory();
  }

  /**
   * 获取补全建议
   */
  public async getCompletions(input: string, cursorPosition: number = input.length): Promise<CompletionItem[]> {
    const beforeCursor = input.slice(0, cursorPosition);
    const afterCursor = input.slice(cursorPosition);
    
    // 根据输入类型提供不同的补全
    if (beforeCursor.startsWith('/')) {
      return this.getCommandCompletions(beforeCursor.slice(1));
    }
    
    if (beforeCursor.startsWith('!')) {
      return this.getBashCompletions(beforeCursor.slice(1));
    }
    
    if (beforeCursor.startsWith('@')) {
      return this.getFileCompletions(beforeCursor.slice(1));
    }
    
    if (beforeCursor.startsWith('#')) {
      return this.getMemoryCompletions(beforeCursor.slice(1));
    }
    
    // 普通文本的智能补全
    return this.getContextualCompletions(beforeCursor);
  }

  /**
   * 获取命令补全
   */
  private getCommandCompletions(partial: string): CompletionItem[] {
    const commands = [
      {
        label: 'help',
        detail: '显示帮助信息',
        documentation: '显示所有可用命令和快捷键的详细帮助',
        insertText: 'help',
        priority: 10
      },
      {
        label: 'config',
        detail: '配置管理',
        documentation: '管理API密钥和系统配置',
        insertText: 'config',
        priority: 9
      },
      {
        label: 'models',
        detail: '模型状态',
        documentation: '查看可用的AI模型和状态',
        insertText: 'models',
        priority: 8
      },
      {
        label: 'memory',
        detail: '项目记忆',
        documentation: '查看和管理项目记忆信息',
        insertText: 'memory',
        priority: 8
      },
      {
        label: 'init',
        detail: '初始化项目',
        documentation: '扫描项目结构并创建记忆文件',
        insertText: 'init',
        priority: 7
      },
      {
        label: 'exit',
        detail: '退出程序',
        documentation: '安全退出Chater AI助手',
        insertText: 'exit',
        priority: 6
      }
    ];

    return commands
      .filter(cmd => cmd.label.toLowerCase().includes(partial.toLowerCase()))
      .map(cmd => ({
        ...cmd,
        kind: CompletionKind.Command,
        icon: '⚡'
      }));
  }

  /**
   * 获取Bash命令补全
   */
  private getBashCompletions(partial: string): CompletionItem[] {
    const bashCommands = [
      // Git 命令
      { label: 'git status', detail: 'Git状态', category: 'git', priority: 10 },
      { label: 'git add .', detail: '添加所有文件', category: 'git', priority: 9 },
      { label: 'git commit -m', detail: '提交更改', category: 'git', priority: 9 },
      { label: 'git push', detail: '推送到远程', category: 'git', priority: 8 },
      { label: 'git pull', detail: '拉取更新', category: 'git', priority: 8 },
      { label: 'git log --oneline', detail: '查看提交历史', category: 'git', priority: 7 },
      
      // NPM 命令
      { label: 'npm install', detail: '安装依赖', category: 'npm', priority: 10 },
      { label: 'npm run build', detail: '构建项目', category: 'npm', priority: 9 },
      { label: 'npm run dev', detail: '开发模式', category: 'npm', priority: 9 },
      { label: 'npm test', detail: '运行测试', category: 'npm', priority: 8 },
      { label: 'npm run lint', detail: '代码检查', category: 'npm', priority: 7 },
      
      // Docker 命令
      { label: 'docker ps', detail: '查看容器', category: 'docker', priority: 8 },
      { label: 'docker images', detail: '查看镜像', category: 'docker', priority: 7 },
      { label: 'docker-compose up', detail: '启动服务', category: 'docker', priority: 8 },
      
      // 系统命令
      { label: 'ls -la', detail: '列出文件', category: 'system', priority: 9 },
      { label: 'pwd', detail: '当前目录', category: 'system', priority: 7 },
      { label: 'cd ..', detail: '返回上级', category: 'system', priority: 6 },
      { label: 'mkdir', detail: '创建目录', category: 'system', priority: 6 }
    ];

    // 添加package.json中的scripts
    if (this.packageJson?.scripts) {
      Object.keys(this.packageJson.scripts).forEach(script => {
        bashCommands.push({
          label: `npm run ${script}`,
          detail: `运行脚本: ${script}`,
          category: 'npm',
          priority: 8
        });
      });
    }

    return bashCommands
      .filter(cmd => cmd.label.toLowerCase().includes(partial.toLowerCase()))
      .map(cmd => ({
        label: cmd.label,
        detail: cmd.detail,
        documentation: `执行命令: ${cmd.label}`,
        kind: cmd.category === 'git' ? CompletionKind.GitCommand : 
              cmd.category === 'npm' ? CompletionKind.NpmScript : 
              CompletionKind.BashCommand,
        insertText: cmd.label,
        priority: cmd.priority,
        icon: this.getBashIcon(cmd.category)
      }));
  }

  /**
   * 获取文件补全
   */
  private async getFileCompletions(partial: string): Promise<CompletionItem[]> {
    const completions: CompletionItem[] = [];
    
    try {
      // 处理路径
      const isAbsolute = partial.startsWith('/');
      const searchPath = isAbsolute ? partial : join(this.projectPath, partial);
      const dirPath = dirname(searchPath);
      const fileName = basename(searchPath);
      
      // 获取目录内容
      if (existsSync(dirPath)) {
        const items = readdirSync(dirPath);
        
        for (const item of items) {
          if (item.toLowerCase().includes(fileName.toLowerCase())) {
            const fullPath = join(dirPath, item);
            const stat = statSync(fullPath);
            const relativePath = isAbsolute ? fullPath : 
              fullPath.replace(this.projectPath + '/', '');
            
            completions.push({
              label: item,
              detail: stat.isDirectory() ? '目录' : this.getFileType(item),
              documentation: `${stat.isDirectory() ? '目录' : '文件'}: ${relativePath}`,
              kind: stat.isDirectory() ? CompletionKind.Directory : CompletionKind.File,
              insertText: relativePath,
              priority: this.getFilePriority(item, stat.isDirectory()),
              icon: stat.isDirectory() ? '📁' : this.getFileIcon(item)
            });
          }
        }
      }
      
      // 添加glob模式建议
      if (partial.includes('*') || partial.includes('?')) {
        const globResults = await glob(partial, { cwd: this.projectPath });
        globResults.slice(0, 10).forEach(file => {
          completions.push({
            label: file,
            detail: 'Glob匹配',
            documentation: `文件匹配: ${file}`,
            kind: CompletionKind.File,
            insertText: file,
            priority: 6,
            icon: '🔍'
          });
        });
      }
      
    } catch (error) {
      // 忽略文件系统错误
    }
    
    return completions.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 获取记忆补全
   */
  private getMemoryCompletions(partial: string): CompletionItem[] {
    const memoryCommands = [
      {
        label: 'insight',
        detail: '记录重要洞察',
        documentation: '添加项目相关的重要发现和洞察',
        insertText: 'insight ',
        priority: 10
      },
      {
        label: 'todo',
        detail: '添加待办事项',
        documentation: '记录需要完成的任务和待办事项',
        insertText: 'todo ',
        priority: 9
      },
      {
        label: 'view',
        detail: '查看记忆内容',
        documentation: '显示项目记忆的详细内容',
        insertText: 'view',
        priority: 8
      },
      {
        label: 'search',
        detail: '搜索记忆',
        documentation: '在项目记忆中搜索特定内容',
        insertText: 'search ',
        priority: 7
      },
      {
        label: 'clear',
        detail: '清除记忆',
        documentation: '清除项目记忆内容（谨慎使用）',
        insertText: 'clear',
        priority: 5
      }
    ];

    return memoryCommands
      .filter(cmd => cmd.label.toLowerCase().includes(partial.toLowerCase()))
      .map(cmd => ({
        ...cmd,
        kind: CompletionKind.Memory,
        icon: '🧠'
      }));
  }

  /**
   * 获取上下文补全
   */
  private getContextualCompletions(input: string): CompletionItem[] {
    const completions: CompletionItem[] = [];
    
    // AI模板建议
    const aiTemplates = [
      {
        label: '帮我分析项目结构',
        detail: '项目分析',
        documentation: '让AI分析当前项目的结构和架构',
        priority: 9
      },
      {
        label: '生成代码文档',
        detail: '文档生成',
        documentation: '为项目代码生成详细文档',
        priority: 8
      },
      {
        label: '优化代码性能',
        detail: '性能优化',
        documentation: '分析并优化代码性能',
        priority: 8
      },
      {
        label: '重构代码结构',
        detail: '代码重构',
        documentation: '改进代码结构和可维护性',
        priority: 7
      },
      {
        label: '添加单元测试',
        detail: '测试编写',
        documentation: '为代码添加单元测试',
        priority: 7
      }
    ];

    // 根据输入内容过滤模板
    const relevantTemplates = aiTemplates.filter(template =>
      template.label.toLowerCase().includes(input.toLowerCase()) ||
      template.detail.toLowerCase().includes(input.toLowerCase())
    );

    relevantTemplates.forEach(template => {
      completions.push({
        ...template,
        kind: CompletionKind.AITemplate,
        insertText: template.label,
        icon: '🤖'
      });
    });

    // 历史记录建议
    const historyMatches = this.commandHistory
      .filter(cmd => cmd.toLowerCase().includes(input.toLowerCase()))
      .slice(0, 3);

    historyMatches.forEach(cmd => {
      completions.push({
        label: cmd,
        detail: '历史记录',
        documentation: `之前使用过的命令: ${cmd}`,
        kind: CompletionKind.Command,
        insertText: cmd,
        priority: 6,
        icon: '📚'
      });
    });

    return completions.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 获取Bash图标
   */
  private getBashIcon(category: string): string {
    const icons: Record<string, string> = {
      git: '🔀',
      npm: '📦',
      docker: '🐳',
      system: '💻'
    };
    return icons[category] || '⚡';
  }

  /**
   * 获取文件图标
   */
  private getFileIcon(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const icons: Record<string, string> = {
      ts: '🔷',
      js: '🟨',
      json: '📋',
      md: '📝',
      py: '🐍',
      java: '☕',
      cpp: '⚙️',
      html: '🌐',
      css: '🎨',
      png: '🖼️',
      jpg: '🖼️',
      gif: '🖼️'
    };
    return icons[ext || ''] || '📄';
  }

  /**
   * 获取文件类型
   */
  private getFileType(filename: string): string {
    const ext = filename.split('.').pop()?.toLowerCase();
    const types: Record<string, string> = {
      ts: 'TypeScript',
      js: 'JavaScript',
      json: 'JSON配置',
      md: 'Markdown文档',
      py: 'Python脚本',
      java: 'Java源码',
      cpp: 'C++源码',
      html: 'HTML页面',
      css: '样式表'
    };
    return types[ext || ''] || '文件';
  }

  /**
   * 获取文件优先级
   */
  private getFilePriority(filename: string, isDirectory: boolean): number {
    if (isDirectory) return 8;
    
    const importantFiles = ['package.json', 'README.md', 'tsconfig.json'];
    if (importantFiles.includes(filename)) return 10;
    
    const ext = filename.split('.').pop()?.toLowerCase();
    const priorities: Record<string, number> = {
      ts: 9,
      js: 8,
      json: 7,
      md: 6
    };

    return priorities[ext || ''] || 5;
  }

  /**
   * 加载package.json
   */
  private loadPackageJson(): void {
    try {
      const packagePath = join(this.projectPath, 'package.json');
      if (existsSync(packagePath)) {
        this.packageJson = require(packagePath);
      }
    } catch (error) {
      // 忽略加载错误
    }
  }

  /**
   * 加载命令历史
   */
  private loadCommandHistory(): void {
    try {
      const fs = require('fs');
      const historyFile = join(this.projectPath, '.chater_history');
      
      if (existsSync(historyFile)) {
        const content = fs.readFileSync(historyFile, 'utf8');
        this.commandHistory = content.split('\n').filter((line: string) => line.trim());
      }
    } catch (error) {
      // 忽略历史记录加载错误
    }
  }

  /**
   * 模糊匹配
   */
  public fuzzyMatch(input: string, target: string): number {
    const inputLower = input.toLowerCase();
    const targetLower = target.toLowerCase();
    
    if (targetLower.includes(inputLower)) {
      return targetLower.indexOf(inputLower) === 0 ? 10 : 8;
    }
    
    // 简单的模糊匹配算法
    let score = 0;
    let inputIndex = 0;
    
    for (let i = 0; i < targetLower.length && inputIndex < inputLower.length; i++) {
      if (targetLower[i] === inputLower[inputIndex]) {
        score++;
        inputIndex++;
      }
    }
    
    return inputIndex === inputLower.length ? score : 0;
  }
}
