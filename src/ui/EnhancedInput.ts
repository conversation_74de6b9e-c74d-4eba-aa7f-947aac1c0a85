import chalk from 'chalk';
import inquirer from 'inquirer';
import { EventEmitter } from 'events';

export interface InputSuggestion {
  text: string;
  description: string;
  type: 'command' | 'file' | 'bash' | 'memory' | 'ai' | 'history';
  icon: string;
  priority: number;
}

export interface InputOptions {
  placeholder?: string;
  multiline?: boolean;
  suggestions?: InputSuggestion[];
  syntaxHighlight?: boolean;
  showPreview?: boolean;
}

/**
 * 增强型输入组件
 * 提供类似 IDE 的输入体验
 */
export class EnhancedInput extends EventEmitter {
  private currentInput = '';
  private suggestions: InputSuggestion[] = [];
  private selectedSuggestion = -1;
  private showingSuggestions = false;
  private inputHistory: string[] = [];
  private historyIndex = -1;

  constructor() {
    super();
    this.loadHistory();
  }

  /**
   * 创建增强输入界面
   */
  public async createInput(prompt: string, options: InputOptions = {}): Promise<string> {
    const {
      placeholder = '输入命令或问题...',
      multiline = false,
      suggestions = [],
      syntaxHighlight = true,
      showPreview = true
    } = options;

    this.suggestions = suggestions;
    
    // 渲染输入框架
    this.renderInputFrame(prompt, placeholder);
    
    if (multiline) {
      return this.handleMultilineInput();
    } else {
      return this.handleSingleLineInput();
    }
  }

  /**
   * 渲染输入框架
   */
  private renderInputFrame(prompt: string, placeholder: string): void {
    const width = 80;
    
    console.log('┌' + '─'.repeat(width - 2) + '┐');
    console.log('│ ' + chalk.cyan(prompt) + ' '.repeat(width - prompt.length - 3) + '│');
    console.log('├' + '─'.repeat(width - 2) + '┤');
    console.log('│ ' + chalk.gray(placeholder) + ' '.repeat(width - placeholder.length - 3) + '│');
    console.log('└' + '─'.repeat(width - 2) + '┘');
  }

  /**
   * 处理单行输入
   */
  private async handleSingleLineInput(): Promise<string> {
    const { input } = await inquirer.prompt([
      {
        type: 'input',
        name: 'input',
        message: this.getFormattedPrompt(),
        validate: this.validateInput.bind(this),
        transformer: this.transformInput.bind(this),
        filter: this.filterInput.bind(this)
      }
    ]);

    this.addToHistory(input);
    return input;
  }

  /**
   * 处理多行输入
   */
  private async handleMultilineInput(): Promise<string> {
    const { input } = await inquirer.prompt([
      {
        type: 'editor',
        name: 'input',
        message: '打开编辑器输入多行内容',
        validate: this.validateInput.bind(this)
      }
    ]);

    this.addToHistory(input);
    return input;
  }

  /**
   * 获取格式化的提示符
   */
  private getFormattedPrompt(): string {
    const icons: Record<string, string> = {
      command: '⚡',
      file: '📁',
      bash: '💻',
      memory: '🧠',
      ai: '🤖',
      history: '📚',
      default: '>'
    };

    const inputType = this.detectInputType(this.currentInput);
    const icon = icons[inputType] || icons.default;
    
    return chalk.cyan(`${icon} `);
  }

  /**
   * 检测输入类型
   */
  private detectInputType(input: string): 'command' | 'file' | 'bash' | 'memory' | 'ai' | 'history' {
    if (input.startsWith('/')) return 'command';
    if (input.startsWith('!')) return 'bash';
    if (input.startsWith('@')) return 'file';
    if (input.startsWith('#')) return 'memory';
    return 'ai';
  }

  /**
   * 验证输入
   */
  private validateInput(input: string): boolean | string {
    if (!input.trim()) {
      return '请输入内容';
    }

    // 特殊命令验证
    if (input.trim() === '?') {
      this.showQuickHelp();
      return true;
    }

    if (input.trim() === '??') {
      this.showSuggestionMenu();
      return true;
    }

    return true;
  }

  /**
   * 转换输入显示
   */
  private transformInput(input: string): string {
    this.currentInput = input;
    
    // 语法高亮
    const highlighted = this.applySyntaxHighlight(input);
    
    // 显示建议计数
    const suggestionCount = this.getRelevantSuggestions(input).length;
    const suggestionHint = suggestionCount > 0 ? 
      chalk.gray(` (${suggestionCount} 个建议)`) : '';
    
    return highlighted + suggestionHint;
  }

  /**
   * 过滤输入
   */
  private filterInput(input: string): string {
    return input.trim();
  }

  /**
   * 应用语法高亮
   */
  private applySyntaxHighlight(input: string): string {
    if (!input) return input;

    // 快捷键高亮
    if (input.startsWith('/')) {
      return chalk.blue('/') + chalk.white(input.slice(1));
    }
    if (input.startsWith('!')) {
      return chalk.green('!') + chalk.white(input.slice(1));
    }
    if (input.startsWith('@')) {
      return chalk.yellow('@') + chalk.white(input.slice(1));
    }
    if (input.startsWith('#')) {
      return chalk.magenta('#') + chalk.white(input.slice(1));
    }

    // 关键词高亮
    return input
      .replace(/\b(help|config|models|memory|exit)\b/g, chalk.blue('$1'))
      .replace(/\b(git|npm|docker|ls|cd|mkdir)\b/g, chalk.green('$1'))
      .replace(/\.(ts|js|json|md|py|java|cpp)\b/g, chalk.yellow('$1'));
  }

  /**
   * 获取相关建议
   */
  private getRelevantSuggestions(input: string): InputSuggestion[] {
    if (!input) return this.getDefaultSuggestions();

    return this.suggestions.filter(suggestion => 
      suggestion.text.toLowerCase().includes(input.toLowerCase()) ||
      suggestion.description.toLowerCase().includes(input.toLowerCase())
    ).sort((a, b) => b.priority - a.priority);
  }

  /**
   * 获取默认建议
   */
  private getDefaultSuggestions(): InputSuggestion[] {
    return [
      {
        text: '/help',
        description: '显示帮助信息',
        type: 'command',
        icon: '📖',
        priority: 10
      },
      {
        text: '!git status',
        description: '查看Git状态',
        type: 'bash',
        icon: '📊',
        priority: 9
      },
      {
        text: '@package.json',
        description: '查看项目配置',
        type: 'file',
        icon: '📦',
        priority: 8
      },
      {
        text: '帮我分析项目结构',
        description: 'AI分析项目',
        type: 'ai',
        icon: '🤖',
        priority: 7
      }
    ];
  }

  /**
   * 显示快速帮助
   */
  private showQuickHelp(): void {
    console.log('\n' + chalk.blue('🚀 快速帮助'));
    console.log('┌─────────────────────────────────────────────────────────────┐');
    console.log('│ ' + chalk.blue('/') + ' 命令  │ ' + chalk.green('!') + ' Bash  │ ' + chalk.yellow('@') + ' 文件  │ ' + chalk.magenta('#') + ' 记忆  │ ' + chalk.cyan('??') + ' 建议 │');
    console.log('└─────────────────────────────────────────────────────────────┘');
    console.log();
  }

  /**
   * 显示建议菜单
   */
  private async showSuggestionMenu(): Promise<string | null> {
    const suggestions = this.getRelevantSuggestions(this.currentInput);
    
    if (suggestions.length === 0) {
      console.log(chalk.yellow('💡 暂无建议'));
      return null;
    }

    const choices = suggestions.slice(0, 8).map(suggestion => ({
      name: `${suggestion.icon} ${suggestion.text} ${chalk.gray('- ' + suggestion.description)}`,
      value: suggestion.text,
      short: suggestion.text
    }));

    choices.unshift({
      name: chalk.gray('💬 继续当前输入'),
      value: '__CONTINUE__',
      short: '继续输入'
    });

    const { selected } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selected',
        message: '选择建议:',
        choices,
        pageSize: 10
      }
    ]);

    return selected === '__CONTINUE__' ? null : selected;
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(input: string): void {
    if (input && !this.inputHistory.includes(input)) {
      this.inputHistory.unshift(input);
      if (this.inputHistory.length > 100) {
        this.inputHistory = this.inputHistory.slice(0, 100);
      }
      this.saveHistory();
    }
  }

  /**
   * 加载历史记录
   */
  private loadHistory(): void {
    try {
      const fs = require('fs');
      const path = require('path');
      const historyFile = path.join(process.cwd(), '.chater_input_history');
      
      if (fs.existsSync(historyFile)) {
        const content = fs.readFileSync(historyFile, 'utf8');
        this.inputHistory = content.split('\n').filter((line: string) => line.trim());
      }
    } catch (error) {
      // 忽略历史记录加载错误
    }
  }

  /**
   * 保存历史记录
   */
  private saveHistory(): void {
    try {
      const fs = require('fs');
      const path = require('path');
      const historyFile = path.join(process.cwd(), '.chater_input_history');
      
      fs.writeFileSync(historyFile, this.inputHistory.join('\n'), 'utf8');
    } catch (error) {
      // 忽略历史记录保存错误
    }
  }

  /**
   * 显示输入预览
   */
  public renderInputPreview(input: string): void {
    if (!input) return;

    const type = this.detectInputType(input);
    const preview = this.generatePreview(input, type);
    
    if (preview) {
      console.log(chalk.gray('📋 预览: ') + preview);
    }
  }

  /**
   * 生成输入预览
   */
  private generatePreview(input: string, type: string): string {
    switch (type) {
      case 'command':
        return `执行命令: ${input.slice(1)}`;
      case 'bash':
        return `运行: ${input.slice(1)}`;
      case 'file':
        return `查看文件: ${input.slice(1)}`;
      case 'memory':
        return `记忆操作: ${input.slice(1)}`;
      default:
        return `AI对话: ${input.slice(0, 50)}${input.length > 50 ? '...' : ''}`;
    }
  }
}
