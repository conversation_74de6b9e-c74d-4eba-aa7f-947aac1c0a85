# Chater 🤖

一个智能的AI聊天代理CLI工具，基于LangChain框架构建，支持多种AI模型。

## 特性

- 🚀 **多模型支持**: DeepSeek、OpenAI GPT、Claude、Google Gemini
- 💬 **智能对话**: 基于LangChain的先进对话管理
- ⚙️ **灵活配置**: 简单的API密钥和偏好设置
- 🎨 **现代化界面**: 类似 Claude Code 的美观 CLI 体验
- 🧠 **智能补全**: 实时建议和内容补全
- 📊 **状态栏**: 项目信息、记忆状态、系统监控
- ✨ **视觉动画**: 流畅的加载和反馈效果
- 📝 **会话管理**: 支持多轮对话上下文
- 🔄 **向后兼容**: 传统界面完整保留

## 快速开始

### 安装

```bash
npm install -g chater
```

### 配置API密钥

```bash
chater config
```

选择要配置的AI服务商，输入对应的API密钥。

### 开始聊天

#### 🎨 现代化界面 (推荐)
```bash
chater --modern
```

#### 🔧 传统界面
```bash
chater --classic
```

#### 默认启动
```bash
chater
```

### 🎯 界面对比

| 特性 | 现代化界面 | 传统界面 |
|------|------------|----------|
| 视觉设计 | 🎨 类似 Claude Code | 🔧 简洁命令行 |
| 输入区域 | 📝 专门的输入框 | ⌨️ 标准提示符 |
| 智能补全 | 🧠 实时建议 | ❌ 无 |
| 状态显示 | 📊 丰富状态栏 | 💡 基础提示 |
| 动画效果 | ✨ 流畅动画 | ❌ 无 |
| 启动速度 | 🚀 快速 | ⚡ 极快 |

## 命令

### `chater chat`
开始一个聊天会话。

选项：
- `-m, --model <model>` - 指定要使用的模型

### `chater config`
配置API密钥和设置。

### `chater models`
列出所有可用的模型及其状态。

## 支持的模型

- **DeepSeek**: `deepseek-chat`, `deepseek-coder`
- **OpenAI**: `gpt-3.5-turbo`, `gpt-4`
- **Anthropic**: `claude-3-sonnet`

## 聊天命令

在聊天过程中，你可以使用以下命令：

- `/exit` - 退出聊天
- `/help` - 显示帮助信息
- `/clear` - 清除当前会话
- `/models` - 显示可用模型

## 配置文件

配置文件位置: `~/.chaterrc.json`

示例配置：
```json
{
  "defaultModel": "deepseek-chat",
  "apiKeys": {
    "deepseek": "your-deepseek-api-key",
    "openai": "your-openai-api-key"
  },
  "models": {
    "deepseek-chat": {
      "provider": "deepseek",
      "model": "deepseek-chat",
      "temperature": 0.7,
      "maxTokens": 2000
    }
  }
}
```

## 开发

```bash
# 克隆仓库
git clone https://github.com/yourusername/chater.git
cd chater

# 安装依赖
npm install

# 开发模式运行
npm run dev chat

# 构建
npm run build
```

## 许可证

MIT License

## 贡献

欢迎贡献代码！请先fork这个仓库，然后提交Pull Request。

## 支持

如果你遇到问题或有建议，请在GitHub上创建issue。