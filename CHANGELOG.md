# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.3.3] - 2025-01-02

### Added
- 🎨 **现代化 CLI 界面**: 类似 Claude Code 的美观设计
- 📝 **专门的输入区域**: 独立输入框，清晰界面分离
- 🧠 **智能内容补全**: 实时建议和自动补全功能
- 📊 **丰富状态栏**: 项目信息、记忆状态、模型状态、系统监控
- ✨ **流畅视觉动画**: 加载指示器、成功反馈、状态切换动画
- 🔧 **命令行选项**: `--modern` 和 `--classic` 界面选择
- 🏗️ **模块化架构**: 5个专门的UI组件 (ModernCLI, EnhancedInput, IntelliSense, StatusBar, Animations)

### Changed
- 📁 **记忆文件格式**: 从 `.chat.md` 更改为 `CHATER.md`
- 🎯 **用户体验**: 显著提升视觉效果和交互体验
- 📚 **文档更新**: 新增现代化界面使用指南

### Fixed
- 🐛 修复所有 TypeScript 编译错误
- 🔧 解决 Chalk API 兼容性问题
- ⚙️ 修复 Inquirer.js 参数问题

## [0.3.0] - 2025-07-01

### Added
- feat: 添加模块功能模块 (package-lock.json, package.json)
- feat(tools): 添加工具功能模块
- feat(src): 功能改进 (toolManager.ts)
- feat: 重新设计交互式 CLI，打造统一开发环境 🚀
- feat: 添加项目记忆系统，让 chater 更加智能
- feat: 实现 Chater AI 聊天代理 CLI 工具

### Changed
- 0.2.1
- Update modified files and add new files
- Initial commit

### Fixed
- fix: 修复核心错误处理机制
- fix: 修复 DeepSeek API "Empty input messages" 错误
- fix: 添加 .gitignore 并清理不必要的文件

### Security
- security: 移除硬编码的 API 密钥并添加项目文档

