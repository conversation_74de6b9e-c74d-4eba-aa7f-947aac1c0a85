# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.3.0] - 2025-07-01

### Added
- feat: 添加模块功能模块 (package-lock.json, package.json)
- feat(tools): 添加工具功能模块
- feat(src): 功能改进 (toolManager.ts)
- feat: 重新设计交互式 CLI，打造统一开发环境 🚀
- feat: 添加项目记忆系统，让 chater 更加智能
- feat: 实现 Chater AI 聊天代理 CLI 工具

### Changed
- 0.2.1
- Update modified files and add new files
- Initial commit

### Fixed
- fix: 修复核心错误处理机制
- fix: 修复 DeepSeek API "Empty input messages" 错误
- fix: 添加 .gitignore 并清理不必要的文件

### Security
- security: 移除硬编码的 API 密钥并添加项目文档

